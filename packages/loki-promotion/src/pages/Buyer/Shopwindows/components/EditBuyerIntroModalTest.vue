<template>
  <div class="test-page">
    <h2>EditBuyerIntroModal Loading 测试</h2>
    <Button @click="openModal">打开编辑简介弹框</Button>
    
    <EditBuyerIntroModal
      v-model:show="showModal"
      :buyer-data="testBuyerData"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button, toast } from '@xhs/delight'
import EditBuyerIntroModal from './EditBuyerIntroModal.vue'

const showModal = ref(false)
const testBuyerData = ref({
  id: 'test-buyer-123',
  name: '测试买手',
  intro: '这是一个测试买手的简介'
})

const openModal = () => {
  showModal.value = true
}

// 模拟异步操作
const handleConfirm = async (data: { id: string; intro: string }) => {
  console.log('开始处理确认事件:', data)
  
  // 模拟 API 请求延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 模拟随机成功/失败
  const isSuccess = Math.random() > 0.3
  
  if (isSuccess) {
    toast({
      type: 'success',
      description: '简介更新成功',
      duration: 2000,
    })
    console.log('操作成功完成')
    showModal.value = false
  } else {
    toast({
      type: 'error',
      description: '更新失败，请重试',
      duration: 2000,
    })
    console.log('操作失败')
    throw new Error('模拟的更新失败')
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
}
</style>
