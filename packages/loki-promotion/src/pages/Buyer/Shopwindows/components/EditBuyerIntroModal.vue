<template>
  <Modal
    v-model:visible="visible"
    title="编辑简介"
    :width="500"
    :mask-closable="false"
    :loading="submitLoading"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="edit-intro-modal">
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="0"
      >
        <FormItem
          name="intro"
          required
        >
          <TextArea
            v-model="formData.intro"
            placeholder="请输入买手简介"
            :max-length="24"
            :rows="4"
            style="width: 100%"
            @change="handleInput"
          />
        </FormItem>
      </Form>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import {
    Form2 as Form,
    FormItem2 as FormItem,
    Modal,
    TextArea,
    toast,
  } from '@xhs/delight'
  import {
    computed,
    ref,
    watch,
  } from 'vue'

  // Props
  interface Props {
    show: boolean
    buyerData?: {
      id: string
      name: string
      intro?: string
    }
  }

  // Emits
  interface Emits {
    (e: 'update:show', value: boolean): void
    (e: 'confirm', data: { id: string; intro: string }): Promise<void>
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const formRef = ref()
  const currentLength = ref(0)
  const submitLoading = ref(false)

  // 表单数据
  const formData = ref({
    intro: '',
  })

  // 表单验证规则
  const formRules = {
    intro: [
      { required: true, message: '请输入买手简介', trigger: 'blur' as const },
      { max: 24, message: '简介不能超过24个字符', trigger: 'blur' as const },
    ],
  }

  // 弹框显示状态
  const visible = computed({
    get: () => props.show,
    set: (value: boolean) => emit('update:show', value),
  })

  // 重置表单
  const resetForm = () => {
    formData.value = {
      intro: '',
    }
    currentLength.value = 0
    submitLoading.value = false
    formRef.value?.resetFields()
  }

  // 处理输入
  const handleInput = (value: string | Event) => {
    if (typeof value === 'string') {
      currentLength.value = value.length
    } else {
      // 如果是Event类型，从target中获取值
      const target = value.target as HTMLTextAreaElement
      currentLength.value = target.value.length
    }
  }

  // 确认处理
  const handleConfirm = async () => {
    submitLoading.value = true
    try {
      await formRef.value?.validate()

      if (!props.buyerData?.id) {
        toast({
          type: 'warning',
          description: '买手信息不完整',
          duration: 2000,
        })
        submitLoading.value = false
        return
      }

      // 等待父组件的异步操作完成
      await emit('confirm', {
        id: props.buyerData.id,
        intro: formData.value.intro,
      })

      // 不在这里重置表单，让父组件关闭弹框后通过 watch 自动重置
      // 这样用户不会看到表单突然清空的过程
    } catch (error) {
      console.error('表单验证失败或操作失败:', error)
    } finally {
      submitLoading.value = false
    }
  }

  // 取消处理
  const handleCancel = () => {
    resetForm()
    emit('update:show', false)
  }

  // 监听弹框显示状态，初始化表单数据
  watch(
    () => props.show,
    newShow => {
      if (newShow && props.buyerData) {
        // 初始化表单数据
        formData.value.intro = props.buyerData.intro || ''
        currentLength.value = formData.value.intro.length
      } else if (!newShow) {
        resetForm()
      }
    },
  )
</script>

<style scoped lang="stylus">
.edit-intro-modal {
  .intro-textarea {
    width: 100%;
    min-height: 120px;
    resize: vertical;
  }

  :deep(.d-textarea) {
    .d-textarea__inner {
      font-size: 14px;
      line-height: 1.5;
    }

    .d-textarea__count {
      color: #999;
      font-size: 12px;
    }
  }
}
</style>
