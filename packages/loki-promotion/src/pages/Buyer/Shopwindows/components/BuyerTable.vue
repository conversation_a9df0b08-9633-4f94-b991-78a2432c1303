<template>
  <div class="buyer-table-container">
    <!-- 当前选中类目显示 -->
    <div
      v-if="searchParams.kolCategoryId"
      class="selected-category"
    >
      <Text>当前类目：</Text>
      <Tag
        closable
        @close="clearCategorySelection"
      >
        类目 ID: {{ searchParams.kolCategoryId }}
      </Tag>
    </div>

    <!-- 搜索区域 -->
    <Form
      ref="searchFormRef"
      :model="searchParams"
      inline
    >
      <div class="search-form-row">
        <FormItem name="searchInput">
          <div class="search-input-group">
            <Dropdown
              v-model="searchFieldType"
              auto-close
              placement="bottom-start"
            >
              <div
                class="search-field-selector"
              >
                {{ currentSearchField.label }}
                <Icon
                  :icon="Down"
                  :size="14"
                />
              </div>
              <template #options>
                <Option
                  v-for="option in searchFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </template>
            </Dropdown>
            <TextArea
              v-model="(searchParams as any).searchValue"
              :placeholder="currentSearchField.placeholder"
              :rows="1"
              style="width: 280px;"
            />
          </div>
        </FormItem>
        <FormItem name="status">
          <Select
            v-model="searchParams.showStatus"
            prefix="展示状态"
            :options="statusOptions"
            placeholder="请选择展示状态（可多选）"
            multiple
            clearable
            style="width: 400px;"
          />
        </FormItem>
      </div>
      <div class="search-actions">
        <Space :gap="12">
          <Button @click="() => { resetSearch(); if (isCategorySelected) handleSearch(); }">重置</Button>
          <Button
            type="secondary"
            :disabled="!isCategorySelected"
            @click="() => handleSearch()"
          >搜索</Button>
        </Space>
      </div>
    </Form>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="left-actions">
        <Button
          type="primary"
          size="small"
          :icon="Plus"
          :disabled="!isCategorySelected"
          @click="showAddModal = true"
        >新增</Button>
        <Dropdown
          auto-close
          placement="bottom"
        >
          <Button
            :icon="Down"
            size="small"
            icon-position="right"
          >批量操作</Button>
          <template #options>
            <Option
              v-for="option in batchOperationOptions"
              :key="option.value"
              :label="option.label"
              :disabled="option.disabled"
              @click="option.onClick"
            />
          </template>
        </Dropdown>
        <Text
          v-if="selectedCount === 0"
          type="description"
        >共 {{ formattedTotal }} 位买手</Text>
        <Text
          v-if="selectedCount > 0"
          type="description"
        >
          已选 {{ formattedSelectedCount }} 位买手
        </Text>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-wrapper">
      <div
        ref="tableBodyRef"
        class="table-body"
      >
        <Table
          :key="tableKey"
          v-model:selected="selectedRows"
          :columns="tableColumns"
          :data-source="tableDataWithKey"
          :loading="loading"
          :row-selection="rowSelection"
          :style="{ height: tableHeight + 'px' }"
          @selectedChange="handleSelectionChange"
        />
      </div>

      <!-- 分页 -->
      <div
        class="data-list-footer"
        style="padding: 10px; display: flex; justify-content: flex-end;"
      >
        <Pagination
          :model-value="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @update:modelValue="onPageChange"
          @update:pageSize="onPageSizeChange"
        />
      </div>
    </div>

    <!-- 新增买手弹框 -->
    <AddBuyerModal
      v-model:show="showAddModal"
      @confirm="handleAddBuyerConfirm"
    />

    <!-- 编辑买手简介弹框 -->
    <EditBuyerIntroModal
      v-model:show="showEditIntroModal"
      :buyer-data="currentEditBuyer"
      @confirm="handleEditBuyerIntroConfirm"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    Button, Dropdown, Form2 as Form, FormItem2 as FormItem, Icon, Option, Pagination, Select, Space, Table, Tag, Text, TextArea,
  } from '@xhs/delight'
  import {
    Down,
    Plus,
  } from '@xhs/delight/icons'
  import {
    computed, onBeforeUnmount, onMounted, ref,
  } from 'vue'
  import { useBuyerTable } from '../hooks/useBuyerTable'
  // @ts-ignore
  import { createTableColumns } from '../utils/tableConfig.tsx'
  import AddBuyerModal from './AddBuyerModal.vue'
  import EditBuyerIntroModal from './EditBuyerIntroModal.vue'

  // 定义props，接收刷新sidebar的回调函数
  interface Props {
    refreshSidebarCallback?: () => Promise<void>
  }

  const props = defineProps<Props>()

  // 使用买手表格hook
  const {
    loading,
    tableData,
    searchParams,
    pagination,
    statusOptions,
    resetSearch,
    handleSearch,
    handleCategoryClick,
    clearCategorySelection,
    deleteBuyer,
    batchOperationOptions,
    selectedRows,
    selectedCount,
    tableKey,
    rowSelection,
    handleSelectionChange,
    searchFieldType,
    searchFieldOptions,
    currentSearchField,
    handleAddBuyer,
    handleEditBuyerIntro,
    editBuyer,
    isCategorySelected,
    onPageChange,
    onPageSizeChange,
  } = useBuyerTable(props.refreshSidebarCallback)

  // 映射table需要的key字段
  const tableDataWithKey = computed(() => tableData.value.map(item => ({ ...item, key: item.kolCategoryUserId })))

  // 千分位格式化后的总数
  const formattedTotal = computed(() => Number(pagination.total || 0).toLocaleString('zh-CN'))

  // 千分位格式化后的已选数量
  const formattedSelectedCount = computed(() => Number(selectedCount.value || 0).toLocaleString('zh-CN'))

  // 新增买手弹框状态
  const showAddModal = ref(false)
  // 编辑简介弹框状态
  const showEditIntroModal = ref(false)
  const currentEditBuyer = ref<any>(null)

  // 表格自适应高度（占据剩余空间 + 固定表头）
  const tableBodyRef = ref<HTMLElement | null>(null)
  const tableHeight = ref(0)
  let tableResizeObserver: ResizeObserver | null = null

  onMounted(() => {
    tableResizeObserver = new ResizeObserver(entries => {
      const rect = entries[0]?.contentRect
      if (rect) {
        tableHeight.value = Math.floor(rect.height)
      }
    })
    if (tableBodyRef.value) tableResizeObserver.observe(tableBodyRef.value)
  })

  onBeforeUnmount(() => {
    tableResizeObserver?.disconnect()
    tableResizeObserver = null
  })

  // 处理新增买手
  const handleAddBuyerConfirm = async (data: { buyerIds: string[]; buyerInfos: any[] }) => {
    await handleAddBuyer(data)
    showAddModal.value = false
  }

  // 处理编辑买手简介
  const handleEditBuyerIntroConfirm = async (data: { id: string; intro: string }) => {
    // 执行异步更新操作
    await handleEditBuyerIntro(data)

    // 异步操作完成后，关闭弹框和清理状态
    showEditIntroModal.value = false
    currentEditBuyer.value = null
  }

  // 重写editBuyer方法，打开编辑简介弹框
  const handleEditBuyerClick = (record: any) => {
    const buyerData = editBuyer(record)
    if (buyerData) {
      currentEditBuyer.value = buyerData
      showEditIntroModal.value = true
    }
  }

  // 表格列配置
  const tableColumns = computed<any>(() => createTableColumns(handleEditBuyerClick, deleteBuyer))

  // 暴露方法供父组件调用
  defineExpose({
    handleCategoryClick,
    clearCategorySelection,
  })
</script>

<style scoped lang="stylus">
.buyer-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  min-width: 0;
  min-height: 0;

  .selected-category {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .search-form-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .search-actions {
    display: flex;
    justify-content: flex-end;
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 4px;

    .left-actions,
    .right-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    overflow: hidden;
    min-height: 0;

    .table-body {
      flex: 1;
      min-height: 0;
      overflow: hidden;
    }

    .loading-indicator {
      padding: 16px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
    }
  }
}

.search-field-selector {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 4px;
  padding: 0 8px;
  width: 50px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  white-space: nowrap;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.search-input-group {
  display: flex;
  align-items: center;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.45);
}

:deep(.d-table) {
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
  }
}
</style>
